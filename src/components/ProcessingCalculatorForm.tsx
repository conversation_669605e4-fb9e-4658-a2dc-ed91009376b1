
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useCalculatorData } from "@/hooks/useCalculatorData";
import { TemplateManager } from "./TemplateManager";
import { ProcessingCustomerAndItemForm } from "./processing/ProcessingCustomerAndItemForm";
import { ProcessingItemsList } from "./processing/ProcessingItemsList";
import { ProcessingLaborAndQuoteForm } from "./processing/ProcessingLaborAndQuoteForm";
import { ProcessingQuoteDisplay } from "./processing/ProcessingQuoteDisplay";


interface ProcessingItem {
  species: string;
  inputWeight: number;
  fromForm: string;
  toForm: string;
  yieldPercentage: number;
  finalWeight: number;
  processingCost: number;
  totalCost: number;
}

interface ProcessingQuote {
  customerName: string;
  items: ProcessingItem[];
  subtotal: number;
  laborCost: number;
  totalCost: number;
}

export const ProcessingCalculatorForm = () => {
  const { species, productForms, fishData, isLoading } = useCalculatorData();
  
  const [formData, setFormData] = useState({
    customerName: "",
    species: "",
    inputWeight: "",
    fromForm: "",
    toForm: "",
    laborHours: "",
    laborRate: "25", // Default labor rate per hour
    notes: "",
  });

  const [items, setItems] = useState<ProcessingItem[]>([]);
  const [quote, setQuote] = useState<ProcessingQuote | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const loadTemplate = (templateData: any) => {
    setFormData({
      customerName: templateData.customerName || "",
      species: templateData.species || "",
      inputWeight: templateData.inputWeight || "",
      fromForm: templateData.fromForm || "",
      toForm: templateData.toForm || "",
      laborHours: templateData.laborHours || "",
      laborRate: templateData.laborRate || "25",
      notes: templateData.notes || "",
    });
    setItems(templateData.items || []);
    setQuote(null);
  };

  const getCurrentTemplateData = () => ({
    ...formData,
    items,
  });

  const getYieldPercentage = () => {
    if (!formData.species || !formData.fromForm || !formData.toForm) return 0;
    
    const matchingData = fishData.data?.find(data => 
      data.species === formData.species &&
      data.from === formData.fromForm &&
      data.to === formData.toForm
    );
    
    return matchingData ? Number(matchingData.average_percentage) : 0;
  };

  const addItem = () => {
    const inputWeight = parseFloat(formData.inputWeight);
    const yieldPercentage = getYieldPercentage();
    
    if (!inputWeight || !yieldPercentage || !formData.species || !formData.fromForm || !formData.toForm) return;

    const finalWeight = (inputWeight * yieldPercentage) / 100;
    
    const toProductForm = productForms.data?.find(form => form.name === formData.toForm);
    const processingCostPerPound = toProductForm?.processing_cost_per_pound || 0;
    
    const processingCost = finalWeight * processingCostPerPound;
    const totalCost = processingCost;

    const newItem: ProcessingItem = {
      species: formData.species,
      inputWeight,
      fromForm: formData.fromForm,
      toForm: formData.toForm,
      yieldPercentage,
      finalWeight,
      processingCost,
      totalCost,
    };

    setItems(prev => [...prev, newItem]);
    
    setFormData(prev => ({
      ...prev,
      species: "",
      inputWeight: "",
      fromForm: "",
      toForm: "",
    }));
  };

  const removeItem = (index: number) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  const generateQuote = () => {
    if (items.length === 0) return;

    const subtotal = items.reduce((sum, item) => sum + item.totalCost, 0);
    const laborHours = parseFloat(formData.laborHours) || 0;
    const laborRate = parseFloat(formData.laborRate) || 0;
    const laborCost = laborHours * laborRate;
    const totalCost = subtotal + laborCost;

    setQuote({
      customerName: formData.customerName,
      items: [...items],
      subtotal,
      laborCost,
      totalCost,
    });
  };

  const resetForm = () => {
    setFormData({
      customerName: "",
      species: "",
      inputWeight: "",
      fromForm: "",
      toForm: "",
      laborHours: "",
      laborRate: "25",
      notes: "",
    });
    setItems([]);
    setQuote(null);
  };

  const getAvailableForms = (type: 'from' | 'to') => {
    if (!formData.species) return [];
    
    const speciesData = fishData.data?.filter(data => data.species === formData.species) || [];
    const forms = type === 'from' 
      ? [...new Set(speciesData.map(data => data.from))]
      : [...new Set(speciesData.filter(data => !formData.fromForm || data.from === formData.fromForm).map(data => data.to))];
    
    return forms.filter(Boolean);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading processing data...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <TemplateManager 
        type="processing"
        currentData={getCurrentTemplateData()}
        onLoadTemplate={loadTemplate}
      />

      <ProcessingCustomerAndItemForm
        formData={formData}
        handleInputChange={handleInputChange}
        addItem={addItem}
        species={species}
        getAvailableForms={getAvailableForms}
        getYieldPercentage={getYieldPercentage}
      />
      
      <ProcessingItemsList items={items} removeItem={removeItem} />

      <ProcessingLaborAndQuoteForm
        formData={formData}
        handleInputChange={handleInputChange}
        generateQuote={generateQuote}
        resetForm={resetForm}
        itemsCount={items.length}
      />

      <ProcessingQuoteDisplay quote={quote} />
    </div>
  );
};
