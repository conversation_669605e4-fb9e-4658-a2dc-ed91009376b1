
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card";

interface ShippingQuote {
  customerName: string;
  shippingAddress: string;
  totalWeight: number;
  shippingMethod: string;
  packagingMaterial: string;
  shippingCost: number;
  packagingCost: number;
  totalCost: number;
}

interface ShippingQuoteDisplayProps {
    quote: ShippingQuote | null;
}

export const ShippingQuoteDisplay = ({ quote }: ShippingQuoteDisplayProps) => {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Shipping Quote</CardTitle>
            </CardHeader>
            <CardContent>
                {quote ? (
                <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div>
                        <strong>Customer:</strong>
                        <p className="text-muted-foreground break-words">{quote.customerName}</p>
                    </div>
                    <div>
                        <strong>Weight:</strong>
                        <p className="text-muted-foreground">{quote.totalWeight} lbs</p>
                    </div>
                    {quote.shippingMethod && (
                        <div>
                        <strong>Shipping Method:</strong>
                        <p className="text-muted-foreground break-words">{quote.shippingMethod}</p>
                        </div>
                    )}
                    <div>
                        <strong>Packaging:</strong>
                        <p className="text-muted-foreground break-words">{quote.packagingMaterial}</p>
                    </div>
                    </div>

                    <div>
                    <strong>Shipping Address:</strong>
                    <p className="text-muted-foreground whitespace-pre-line break-words">{quote.shippingAddress}</p>
                    </div>

                    <div className="border-t pt-4">
                    <div className="space-y-2">
                        {quote.shippingCost > 0 && (
                        <div className="flex justify-between items-center">
                            <span className="text-sm sm:text-base">Shipping Cost:</span>
                            <span className="font-medium">${quote.shippingCost.toFixed(2)}</span>
                        </div>
                        )}
                        <div className="flex justify-between items-center">
                        <span className="text-sm sm:text-base">Packaging Cost:</span>
                        <span className="font-medium">${quote.packagingCost.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between items-center font-bold text-lg border-t pt-2">
                        <span className="text-sm sm:text-base">Total Cost:</span>
                        <span>${quote.totalCost.toFixed(2)}</span>
                        </div>
                    </div>
                    </div>
                </div>
                ) : (
                <div className="text-center text-muted-foreground p-8">
                    <p className="text-sm sm:text-base">Fill out the shipping details and click "Calculate Shipping" to see the quote.</p>
                </div>
                )}
            </CardContent>
        </Card>
    );
};
