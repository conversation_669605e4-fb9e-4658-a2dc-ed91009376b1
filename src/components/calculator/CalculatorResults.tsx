
import { Card } from "@/components/ui/card";

interface Results {
  processingCost: number;
  shippingCost: number;
  packagingCost: number;
  totalCost: number;
  finalPrice: number;
}

interface CalculatorResultsProps {
  results: Results | null;
}

export const CalculatorResults = ({ results }: CalculatorResultsProps) => {
  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-6">Results</h2>
      {results ? (
        <div className="space-y-4">
          <div className="flex justify-between p-3 bg-gray-50 rounded">
            <span>Processing Cost:</span>
            <span className="font-semibold">${results.processingCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between p-3 bg-gray-50 rounded">
            <span>Shipping Cost:</span>
            <span className="font-semibold">${results.shippingCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between p-3 bg-gray-50 rounded">
            <span>Packaging Cost:</span>
            <span className="font-semibold">${results.packagingCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between p-3 bg-gray-50 rounded">
            <span>Total Cost:</span>
            <span className="font-semibold">${results.totalCost.toFixed(2)}</span>
          </div>
          <div className="flex justify-between p-3 bg-gray-50 rounded border-t-2 border-gray-200">
            <span>Final Price:</span>
            <span className="font-semibold">${results.finalPrice.toFixed(2)}</span>
          </div>
        </div>
      ) : (
        <div className="h-full flex items-center justify-center text-gray-500">
          Enter calculation details and click Calculate to see results
        </div>
      )}
    </Card>
  );
};
