
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface Template {
  id: string;
  name: string;
  description?: string;
  template_data: any;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export const useTemplates = (type: 'shipping' | 'processing') => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const tableName = type === 'shipping' ? 'shipping_templates' : 'processing_templates';

  const { data: templates, isLoading } = useQuery({
    queryKey: [tableName],
    queryFn: async () => {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as Template[];
    },
  });

  const saveTemplate = useMutation({
    mutationFn: async ({ name, description, templateData }: { 
      name: string; 
      description?: string; 
      templateData: any;
    }) => {
      const { data: { user } } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from(tableName)
        .insert([{
          name,
          description,
          template_data: templateData,
          created_by: user?.id,
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [tableName] });
      toast({
        title: "Template saved",
        description: "Your template has been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save template. Please try again.",
        variant: "destructive",
      });
    },
  });

  const deleteTemplate = useMutation({
    mutationFn: async (templateId: string) => {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', templateId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [tableName] });
      toast({
        title: "Template deleted",
        description: "Template has been deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete template. Please try again.",
        variant: "destructive",
      });
    },
  });

  return {
    templates: templates || [],
    isLoading,
    saveTemplate: saveTemplate.mutate,
    deleteTemplate: deleteTemplate.mutate,
    isSaving: saveTemplate.isPending,
    isDeleting: deleteTemplate.isPending,
  };
};
