
-- Add missing species column to fish_data table
ALTER TABLE fish_data ADD COLUMN IF NOT EXISTS species VARCHAR(100);

-- Create product_forms table for processing options
CREATE TABLE IF NOT EXISTS product_forms (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    processing_cost_per_pound DECIMAL(10,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create shipping_rates table
CREATE TABLE IF NOT EXISTS shipping_rates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    method VARCHAR(50) NOT NULL,
    region VARCHAR(50),
    rate_per_pound DECIMAL(10,2) NOT NULL,
    min_weight INTEGER DEFAULT 0,
    max_weight INTEGER DEFAULT 50000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create packaging_materials table
CREATE TABLE IF NOT EXISTS packaging_materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    cost_per_unit DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create quotes table for saving calculations
CREATE TABLE IF NOT EXISTS quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    customer_name VARCHAR(100),
    shipping_method VARCHAR(50),
    shipping_address TEXT,
    shipping_cost DECIMAL(10,2),
    packaging_id UUID REFERENCES packaging_materials(id),
    payment_terms VARCHAR(100),
    delivery_timeframe VARCHAR(100),
    subtotal DECIMAL(10,2),
    total DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create quote_items table
CREATE TABLE IF NOT EXISTS quote_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quote_id UUID REFERENCES quotes(id) ON DELETE CASCADE,
    species_id VARCHAR(100),
    product_form_id UUID REFERENCES product_forms(id),
    input_weight DECIMAL(10,2),
    final_weight DECIMAL(10,2),
    price_per_pound DECIMAL(10,2),
    total_price DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert some sample data for testing
INSERT INTO product_forms (name, processing_cost_per_pound) VALUES
    ('Custom Cut', 3.00),
    ('IVP (Individual Vacuum Pack)', 0.50),
    ('Skin-On Fillet', 2.50),
    ('Skinless Fillet', 3.50),
    ('Pin Bone Out', 1.25)
ON CONFLICT DO NOTHING;

INSERT INTO shipping_rates (method, region, rate_per_pound) VALUES
    ('UPS Air', 'Alaska to Seattle', 1.50),
    ('Alaska Air Cargo', 'Alaska to Seattle', 0.75),
    ('FedEx Express', 'Alaska to Seattle', 1.25),
    ('Ground Transport', 'Local', 0.40)
ON CONFLICT DO NOTHING;

INSERT INTO packaging_materials (name, cost_per_unit) VALUES
    ('Standard Box', 2.50),
    ('Insulated Box', 5.00),
    ('Vacuum Bag', 0.25),
    ('Ice Pack', 1.00)
ON CONFLICT DO NOTHING;

-- Update fish_data with some sample species data
UPDATE fish_data SET species = 'King Salmon' WHERE table_name = 'king_salmon' AND species IS NULL;
UPDATE fish_data SET species = 'Sockeye Salmon' WHERE table_name = 'sockeye_salmon' AND species IS NULL;
UPDATE fish_data SET species = 'Coho Salmon' WHERE table_name = 'coho_salmon' AND species IS NULL;
UPDATE fish_data SET species = 'Halibut' WHERE table_name = 'halibut' AND species IS NULL;
UPDATE fish_data SET species = 'Pacific Cod' WHERE table_name = 'pacific_cod' AND species IS NULL;
