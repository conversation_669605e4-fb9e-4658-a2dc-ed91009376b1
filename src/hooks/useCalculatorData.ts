
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export function useCalculatorData() {
  const fetchFishData = async () => {
    console.log("Fetching fish data...");
    const { data, error } = await supabase
      .from("fish_data")
      .select("*")
      .order("species");
    
    console.log("Fish data response:", { data, error });
    if (error) throw error;
    return data || [];
  };

  const fetchSpecies = async () => {
    console.log("Fetching species...");
    const { data, error } = await supabase
      .from("fish_data")
      .select("species")
      .order("species");
    
    console.log("Species response:", { data, error });
    if (error) throw error;
    
    // Get unique species names, filter out null values
    const uniqueSpecies = [...new Set(data?.map(item => item.species).filter(Boolean) || [])];
    return uniqueSpecies.map((species, index) => ({
      id: index.toString(),
      name: species
    }));
  };

  const fetchProductForms = async () => {
    console.log("Fetching product forms...");
    const { data, error } = await supabase
      .from("product_forms")
      .select("*")
      .order("name");
    
    console.log("Product forms response:", { data, error });
    if (error) throw error;
    return data || [];
  };

  const fetchShippingRates = async () => {
    console.log("Fetching shipping rates...");
    const { data, error } = await supabase
      .from("shipping_rates")
      .select("*")
      .order("method");
    
    console.log("Shipping rates response:", { data, error });
    if (error) throw error;
    return data || [];
  };

  const fetchPackagingMaterials = async () => {
    console.log("Fetching packaging materials...");
    const { data, error } = await supabase
      .from("packaging_materials")
      .select("*")
      .order("name");
    
    console.log("Packaging materials response:", { data, error });
    if (error) throw error;
    return data || [];
  };

  const fishData = useQuery({
    queryKey: ["fishData"],
    queryFn: fetchFishData,
    retry: 1,
  });

  const species = useQuery({
    queryKey: ["species"],
    queryFn: fetchSpecies,
    retry: 1,
  });

  const productForms = useQuery({
    queryKey: ["productForms"],
    queryFn: fetchProductForms,
    retry: 1,
  });

  const shippingRates = useQuery({
    queryKey: ["shippingRates"],
    queryFn: fetchShippingRates,
    retry: 1,
  });

  const packagingMaterials = useQuery({
    queryKey: ["packagingMaterials"],
    queryFn: fetchPackagingMaterials,
    retry: 1,
  });

  console.log("Query states:", {
    fishData: { isLoading: fishData.isLoading, isError: fishData.isError, data: fishData.data },
    species: { isLoading: species.isLoading, isError: species.isError, data: species.data },
    productForms: { isLoading: productForms.isLoading, isError: productForms.isError, data: productForms.data },
    shippingRates: { isLoading: shippingRates.isLoading, isError: shippingRates.isError, data: shippingRates.data },
    packagingMaterials: { isLoading: packagingMaterials.isLoading, isError: packagingMaterials.isError, data: packagingMaterials.data },
  });

  return {
    fishData,
    species,
    productForms,
    shippingRates,
    packagingMaterials,
    isLoading: fishData.isLoading || species.isLoading || productForms.isLoading || shippingRates.isLoading || packagingMaterials.isLoading,
    isError: fishData.isError || species.isError || productForms.isError || shippingRates.isError || packagingMaterials.isError,
  };
}
