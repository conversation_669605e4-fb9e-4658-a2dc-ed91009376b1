
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useCalculatorData } from "@/hooks/useCalculatorData";
import { TemplateManager } from "./TemplateManager";
import { ShippingDetailsForm } from "./shipping/ShippingDetailsForm";
import { ShippingQuoteDisplay } from "./shipping/ShippingQuoteDisplay";

interface ShippingQuote {
  customerName: string;
  shippingAddress: string;
  totalWeight: number;
  shippingMethod: string;
  packagingMaterial: string;
  shippingCost: number;
  packagingCost: number;
  totalCost: number;
}

export const ShippingCalculatorForm = () => {
  const { shippingRates, packagingMaterials, isLoading } = useCalculatorData();
  
  const [formData, setFormData] = useState({
    customerName: "",
    shippingAddress: "",
    totalWeight: "",
    shippingMethod: "",
    packagingMaterial: "",
    notes: "",
  });

  const [quote, setQuote] = useState<ShippingQuote | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const loadTemplate = (templateData: any) => {
    setFormData({
      customerName: templateData.customerName || "",
      shippingAddress: templateData.shippingAddress || "",
      totalWeight: templateData.totalWeight || "",
      shippingMethod: templateData.shippingMethod || "",
      packagingMaterial: templateData.packagingMaterial || "",
      notes: templateData.notes || "",
    });
    setQuote(null);
  };

  const calculateShipping = () => {
    const weight = parseFloat(formData.totalWeight);
    if (!weight || !formData.packagingMaterial) return;

    const selectedPackaging = packagingMaterials.data?.find(
      material => material.name === formData.packagingMaterial
    );

    if (!selectedPackaging) return;

    let shippingCost = 0;
    
    // Only calculate shipping cost if shipping method is selected
    if (formData.shippingMethod) {
      const selectedShippingRate = shippingRates.data?.find(
        rate => rate.method === formData.shippingMethod
      );
      
      if (selectedShippingRate) {
        shippingCost = weight * selectedShippingRate.rate_per_pound;
      }
    }

    const packagingCost = selectedPackaging.cost_per_unit;
    const totalCost = shippingCost + packagingCost;

    setQuote({
      customerName: formData.customerName,
      shippingAddress: formData.shippingAddress,
      totalWeight: weight,
      shippingMethod: formData.shippingMethod || "No shipping method selected",
      packagingMaterial: formData.packagingMaterial,
      shippingCost,
      packagingCost,
      totalCost,
    });
  };

  const resetForm = () => {
    setFormData({
      customerName: "",
      shippingAddress: "",
      totalWeight: "",
      shippingMethod: "",
      packagingMaterial: "",
      notes: "",
    });
    setQuote(null);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading shipping data...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="px-2 sm:px-0">
        <TemplateManager 
          type="shipping"
          currentData={formData}
          onLoadTemplate={loadTemplate}
        />
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 px-2 sm:px-0">
        <ShippingDetailsForm 
            formData={formData}
            handleInputChange={handleInputChange}
            calculateShipping={calculateShipping}
            resetForm={resetForm}
            shippingRates={shippingRates}
            packagingMaterials={packagingMaterials}
        />
        <ShippingQuoteDisplay quote={quote} />
      </div>
    </div>
  );
};
