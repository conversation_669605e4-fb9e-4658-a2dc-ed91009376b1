
import { useState, useMemo } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useCalculatorData } from "@/hooks/useCalculatorData";
import { supabase } from "@/integrations/supabase/client";
import { CalculatorForm } from "./calculator/CalculatorForm";
import { CalculatorResults } from "./calculator/CalculatorResults";

export const Calculator = () => {
  const { toast } = useToast();
  const { 
    fishData,
    species, 
    productForms, 
    shippingRates, 
    packagingMaterials, 
    isLoading, 
    isError 
  } = useCalculatorData();

  const [formData, setFormData] = useState({
    speciesName: "",
    fromProductForm: "",
    toProductForm: "",
    averagePercentage: "",
    rangePercentage: "",
    productFormId: "",
    rawCost: "",
    weight: "",
    yield: "",
    shippingRateId: "",
    packagingId: "",
    markup: "",
  });

  const [results, setResults] = useState<{
    processingCost: number;
    shippingCost: number;
    packagingCost: number;
    totalCost: number;
    finalPrice: number;
  } | null>(null);

  // Filter fish data based on selected species
  const filteredFishData = useMemo(() => {
    if (!fishData.data || !formData.speciesName) return [];
    return fishData.data.filter(item => item.species === formData.speciesName);
  }, [fishData.data, formData.speciesName]);

  // Get unique from options for selected species
  const fromOptions = useMemo(() => {
    const uniqueFroms = [...new Set(filteredFishData.map(item => item.from))];
    return uniqueFroms.map((from, index) => ({
      id: index.toString(),
      name: from
    }));
  }, [filteredFishData]);

  // Get unique to options for selected species and from
  const toOptions = useMemo(() => {
    if (!formData.fromProductForm) return [];
    const filteredByFrom = filteredFishData.filter(item => item.from === formData.fromProductForm);
    const uniqueTos = [...new Set(filteredByFrom.map(item => item.to))];
    return uniqueTos.map((to, index) => ({
      id: index.toString(),
      name: to
    }));
  }, [filteredFishData, formData.fromProductForm]);

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({ ...prev, [field]: e.target.value }));
  };

  const handleSelectChange = (field: string) => (value: string) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };
      
      if (field === "speciesName") {
        newData.fromProductForm = "";
        newData.toProductForm = "";
        newData.averagePercentage = "";
        newData.rangePercentage = "";
        newData.yield = "";
      }
      
      if (field === "fromProductForm") {
        newData.toProductForm = "";
        newData.averagePercentage = "";
        newData.rangePercentage = "";
        newData.yield = "";
      }
      
      if (field === "toProductForm") {
        const fishDataEntry = fishData.data?.find(item => 
          item.species === newData.speciesName && 
          item.from === newData.fromProductForm && 
          item.to === value
        );
        if (fishDataEntry) {
          newData.averagePercentage = fishDataEntry.average_percentage?.toString() || "";
          newData.rangePercentage = fishDataEntry.range_percentage || "";
          newData.yield = fishDataEntry.average_percentage?.toString() || "";
        }
      }
      
      return newData;
    });
  };

  const calculateResults = async () => {
    const requiredFields = ['speciesName', 'productFormId', 'rawCost', 'weight', 'yield', 'shippingRateId'];
    const missingFields = requiredFields.filter(field => !formData[field]);
    
    if (missingFields.length > 0) {
      toast({
        variant: "destructive",
        title: "Missing Required Fields",
        description: `Please fill in all required fields`,
      });
      return;
    }

    const selectedProductForm = productForms.data?.find(pf => pf.id === formData.productFormId);
    const selectedShippingRate = shippingRates.data?.find(sr => sr.id === formData.shippingRateId);
    const selectedPackaging = formData.packagingId ? packagingMaterials.data?.find(pm => pm.id === formData.packagingId) : null;

    if (!selectedProductForm || !selectedShippingRate) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Could not find product form or shipping rate",
      });
      return;
    }

    const weight = parseFloat(formData.weight);
    const rawCost = parseFloat(formData.rawCost);
    const yieldPercent = parseFloat(formData.yield);
    const markup = formData.markup ? parseFloat(formData.markup) : 0;

    const processingCost = weight * selectedProductForm.processing_cost_per_pound;
    const shippingCost = weight * selectedShippingRate.rate_per_pound;
    const packagingCost = selectedPackaging ? selectedPackaging.cost_per_unit : 0;
    const totalCost = (rawCost * weight) + processingCost + shippingCost + packagingCost;
    const finalPrice = markup ? totalCost * (1 + markup / 100) : totalCost;

    try {
      const { data: quote, error: quoteError } = await supabase
        .from('quotes')
        .insert({
          customer_name: 'Calculator User',
          shipping_method: selectedShippingRate.method,
          shipping_address: 'N/A',
          shipping_cost: shippingCost,
          packaging_id: formData.packagingId || null,
          payment_terms: 'Standard',
          delivery_timeframe: 'Standard',
          subtotal: totalCost - shippingCost,
          total: finalPrice,
        })
        .select()
        .single();

      if (quoteError) throw quoteError;

      const { error: itemError } = await supabase
        .from('quote_items')
        .insert({
          quote_id: quote.id,
          species_id: formData.speciesName,
          product_form_id: formData.productFormId,
          input_weight: weight,
          final_weight: weight * (yieldPercent / 100),
          price_per_pound: rawCost,
          total_price: finalPrice,
        });

      if (itemError) throw itemError;

      setResults({ processingCost, shippingCost, packagingCost, totalCost, finalPrice });
      toast({ title: "Calculation Complete", description: "Results have been updated and saved" });
    } catch (error) {
      console.error('Error saving calculation:', error);
      setResults({ processingCost, shippingCost, packagingCost, totalCost, finalPrice });
      toast({ variant: "destructive", title: "Calculation Complete", description: "Results calculated (note: saving to database failed)" });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading calculator data...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center h-64 text-red-500">
        <div className="text-center">
          <p className="text-lg font-semibold mb-2">Error loading data</p>
          <p className="text-sm">Please check your database connection and try again.</p>
          <p className="text-xs mt-2">Check the console for more details.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Food Product Cost Calculator</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <CalculatorForm
            formData={formData}
            handleInputChange={handleInputChange}
            handleSelectChange={handleSelectChange}
            calculateResults={calculateResults}
            species={species}
            productForms={productForms}
            shippingRates={shippingRates}
            packagingMaterials={packagingMaterials}
            fromOptions={fromOptions}
            toOptions={toOptions}
        />
        <CalculatorResults results={results} />
      </div>
    </div>
  );
};
