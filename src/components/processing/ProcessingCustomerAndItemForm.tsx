
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface FormData {
    customerName: string;
    species: string;
    inputWeight: string;
    fromForm: string;
    toForm: string;
    laborHours: string;
    laborRate: string;
    notes: string;
}

interface Species {
    id: string;
    name: string;
}

interface ProcessingCustomerAndItemFormProps {
    formData: FormData;
    handleInputChange: (field: string, value: string) => void;
    addItem: () => void;
    species: { data?: Species[] };
    getAvailableForms: (type: 'from' | 'to') => string[];
    getYieldPercentage: () => number;
}

export const ProcessingCustomerAndItemForm = ({
    formData,
    handleInputChange,
    addItem,
    species,
    getAvailableForms,
    getYieldPercentage,
}: ProcessingCustomerAndItemFormProps) => {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Processing Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div>
                    <Label htmlFor="customerName">Customer Name</Label>
                    <Input
                        id="customerName"
                        value={formData.customerName}
                        onChange={(e) => handleInputChange("customerName", e.target.value)}
                        placeholder="Enter customer name"
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <Label htmlFor="species">Species</Label>
                        <Select value={formData.species} onValueChange={(value) => handleInputChange("species", value)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select species" />
                            </SelectTrigger>
                            <SelectContent>
                                {species.data?.map((spec) => (
                                    <SelectItem key={spec.id} value={spec.name}>
                                        {spec.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label htmlFor="inputWeight">Input Weight (lbs)</Label>
                        <Input
                            id="inputWeight"
                            type="number"
                            step="0.01"
                            value={formData.inputWeight}
                            onChange={(e) => handleInputChange("inputWeight", e.target.value)}
                            placeholder="0.00"
                        />
                    </div>

                    <div>
                        <Label htmlFor="fromForm">From Product Form</Label>
                        <Select value={formData.fromForm} onValueChange={(value) => handleInputChange("fromForm", value)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select from form" />
                            </SelectTrigger>
                            <SelectContent>
                                {getAvailableForms('from').map((form) => (
                                    <SelectItem key={form} value={form}>
                                        {form}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label htmlFor="toForm">To Product Form</Label>
                        <Select value={formData.toForm} onValueChange={(value) => handleInputChange("toForm", value)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select to form" />
                            </SelectTrigger>
                            <SelectContent>
                                {getAvailableForms('to').map((form) => (
                                    <SelectItem key={form} value={form}>
                                        {form}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {formData.species && formData.fromForm && formData.toForm && (
                    <div className="bg-muted p-3 rounded-md">
                        <div className="text-sm">
                            <strong>Yield:</strong> {getYieldPercentage()}%
                            {formData.inputWeight && (
                                <span className="ml-4">
                                    <strong>Final Weight:</strong> {((parseFloat(formData.inputWeight) * getYieldPercentage()) / 100).toFixed(2)} lbs
                                </span>
                            )}
                        </div>
                    </div>
                )}

                <Button onClick={addItem} disabled={!formData.species || !formData.inputWeight || !formData.fromForm || !formData.toForm}>
                    Add Item
                </Button>
            </CardContent>
        </Card>
    );
};
