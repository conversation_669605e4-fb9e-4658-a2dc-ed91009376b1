export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      batch_tracking: {
        Row: {
          batch_number: string
          created_at: string
          expiry_date: string | null
          id: string
          notes: string | null
          product_id: string | null
          quality_check_status: string | null
          quantity: number
          received_date: string
          unit_cost: number
          updated_at: string
          vendor_id: string | null
        }
        Insert: {
          batch_number: string
          created_at?: string
          expiry_date?: string | null
          id?: string
          notes?: string | null
          product_id?: string | null
          quality_check_status?: string | null
          quantity: number
          received_date: string
          unit_cost: number
          updated_at?: string
          vendor_id?: string | null
        }
        Update: {
          batch_number?: string
          created_at?: string
          expiry_date?: string | null
          id?: string
          notes?: string | null
          product_id?: string | null
          quality_check_status?: string | null
          quantity?: number
          received_date?: string
          unit_cost?: number
          updated_at?: string
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "batch_tracking_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "batch_tracking_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      batches: {
        Row: {
          batch_number: string
          created_at: string
          created_by: string | null
          expiry_date: string | null
          id: string
          images: string[] | null
          notes: string | null
          product_id: string
          quality: string | null
          quantity: number
          received_date: string
          source: string | null
          suplier_id: string | null
          temperature: number | null
          updated_at: string
          vendor: string | null
        }
        Insert: {
          batch_number: string
          created_at?: string
          created_by?: string | null
          expiry_date?: string | null
          id?: string
          images?: string[] | null
          notes?: string | null
          product_id: string
          quality?: string | null
          quantity: number
          received_date: string
          source?: string | null
          suplier_id?: string | null
          temperature?: number | null
          updated_at?: string
          vendor?: string | null
        }
        Update: {
          batch_number?: string
          created_at?: string
          created_by?: string | null
          expiry_date?: string | null
          id?: string
          images?: string[] | null
          notes?: string | null
          product_id?: string
          quality?: string | null
          quantity?: number
          received_date?: string
          source?: string | null
          suplier_id?: string | null
          temperature?: number | null
          updated_at?: string
          vendor?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "batches_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: true
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "batches_supplier_id_fkey"
            columns: ["suplier_id"]
            isOneToOne: true
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          address: string
          channel_type: string
          contact_name: string
          created_at: string
          credit_limit: number | null
          customer_source: string
          email: string
          id: string
          last_order_date: string | null
          metadata: Json | null
          name: string
          payment_terms: string | null
          phone: string
          status: string
          tags: string[] | null
          total_orders: number | null
          total_revenue: number | null
          updated_at: string
        }
        Insert: {
          address: string
          channel_type: string
          contact_name: string
          created_at?: string
          credit_limit?: number | null
          customer_source: string
          email: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name: string
          payment_terms?: string | null
          phone: string
          status: string
          tags?: string[] | null
          total_orders?: number | null
          total_revenue?: number | null
          updated_at?: string
        }
        Update: {
          address?: string
          channel_type?: string
          contact_name?: string
          created_at?: string
          credit_limit?: number | null
          customer_source?: string
          email?: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name?: string
          payment_terms?: string | null
          phone?: string
          status?: string
          tags?: string[] | null
          total_orders?: number | null
          total_revenue?: number | null
          updated_at?: string
        }
        Relationships: []
      }
      discounts: {
        Row: {
          amount: number
          created_at: string
          event_id: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          event_id?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          event_id?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "discounts_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      disposal_events: {
        Row: {
          authorized_by: string | null
          created_at: string
          created_by: string | null
          disposal_method: string
          event_id: string | null
          id: string
          reason: string
          witness: string | null
        }
        Insert: {
          authorized_by?: string | null
          created_at?: string
          created_by?: string | null
          disposal_method: string
          event_id?: string | null
          id?: string
          reason: string
          witness?: string | null
        }
        Update: {
          authorized_by?: string | null
          created_at?: string
          created_by?: string | null
          disposal_method?: string
          event_id?: string | null
          id?: string
          reason?: string
          witness?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "disposal_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          cogs: number | null
          created_at: string
          created_by: string | null
          customer_id: string | null
          event_type: string | null
          external_refs: Json | null
          gdst_data: Json | null
          id: string
          metadata: Json | null
          move_to_balance_sheet: boolean | null
          notes: string | null
          per_unit_cogs: number | null
          product_id: string | null
          quality_status: string | null
          quantity: number
          temperature: number | null
          transaction_id: string | null
          unit: string
          updated_at: string
          vendor_id: string | null
        }
        Insert: {
          cogs?: number | null
          created_at?: string
          created_by?: string | null
          customer_id?: string | null
          event_type?: string | null
          external_refs?: Json | null
          gdst_data?: Json | null
          id?: string
          metadata?: Json | null
          move_to_balance_sheet?: boolean | null
          notes?: string | null
          per_unit_cogs?: number | null
          product_id?: string | null
          quality_status?: string | null
          quantity: number
          temperature?: number | null
          transaction_id?: string | null
          unit: string
          updated_at?: string
          vendor_id?: string | null
        }
        Update: {
          cogs?: number | null
          created_at?: string
          created_by?: string | null
          customer_id?: string | null
          event_type?: string | null
          external_refs?: Json | null
          gdst_data?: Json | null
          id?: string
          metadata?: Json | null
          move_to_balance_sheet?: boolean | null
          notes?: string | null
          per_unit_cogs?: number | null
          product_id?: string | null
          quality_status?: string | null
          quantity?: number
          temperature?: number | null
          transaction_id?: string | null
          unit?: string
          updated_at?: string
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      fish_data: {
        Row: {
          average_percentage: number | null
          from: string
          id: number
          range_percentage: string | null
          species: string
          to: string
        }
        Insert: {
          average_percentage?: number | null
          from: string
          id?: number
          range_percentage?: string | null
          species: string
          to: string
        }
        Update: {
          average_percentage?: number | null
          from?: string
          id?: number
          range_percentage?: string | null
          species?: string
          to?: string
        }
        Relationships: []
      }
      import_logs: {
        Row: {
          created_at: string
          created_by: string | null
          error_details: Json | null
          error_rows: number
          filename: string
          id: string
          processed_rows: number
          status: string
          total_rows: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          error_details?: Json | null
          error_rows?: number
          filename: string
          id?: string
          processed_rows?: number
          status?: string
          total_rows?: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          error_details?: Json | null
          error_rows?: number
          filename?: string
          id?: string
          processed_rows?: number
          status?: string
          total_rows?: number
          updated_at?: string
        }
        Relationships: []
      }
      inventory: {
        Row: {
          category: string
          cost: number | null
          created_at: string
          event_type: string
          expiry_date: string | null
          handling_instructions: string | null
          id: string
          images: string[] | null
          metadata: Json
          min_stock: number
          name: string
          notes: string
          origin: string | null
          price: number | null
          product_id: string | null
          quantity: number
          seasonal_availability: string[] | null
          source: string | null
          species_details: Json | null
          stock: number
          storage_temp: string | null
          sub_category: string | null
          supplier_id: string | null
          total_amount: number
          unit_price: number
          updated_at: string
        }
        Insert: {
          category: string
          cost?: number | null
          created_at?: string
          event_type?: string
          expiry_date?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          metadata?: Json
          min_stock?: number
          name?: string
          notes?: string
          origin?: string | null
          price?: number | null
          product_id?: string | null
          quantity?: number
          seasonal_availability?: string[] | null
          source?: string | null
          species_details?: Json | null
          stock?: number
          storage_temp?: string | null
          sub_category?: string | null
          supplier_id?: string | null
          total_amount?: number
          unit_price?: number
          updated_at?: string
        }
        Update: {
          category?: string
          cost?: number | null
          created_at?: string
          event_type?: string
          expiry_date?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          metadata?: Json
          min_stock?: number
          name?: string
          notes?: string
          origin?: string | null
          price?: number | null
          product_id?: string | null
          quantity?: number
          seasonal_availability?: string[] | null
          source?: string | null
          species_details?: Json | null
          stock?: number
          storage_temp?: string | null
          sub_category?: string | null
          supplier_id?: string | null
          total_amount?: number
          unit_price?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_inventory_product"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_events: {
        Row: {
          category: string
          created_at: string | null
          event_type: string
          id: string
          images: string[] | null
          metadata: Json | null
          name: string | null
          notes: string | null
          product_id: string | null
          quantity: number | null
          total_amount: number | null
          unit_price: number | null
          updated_at: string | null
        }
        Insert: {
          category?: string
          created_at?: string | null
          event_type: string
          id?: string
          images?: string[] | null
          metadata?: Json | null
          name?: string | null
          notes?: string | null
          product_id?: string | null
          quantity?: number | null
          total_amount?: number | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          event_type?: string
          id?: string
          images?: string[] | null
          metadata?: Json | null
          name?: string | null
          notes?: string | null
          product_id?: string | null
          quantity?: number | null
          total_amount?: number | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_events_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
        ]
      }
      message_queue: {
        Row: {
          created_at: string
          id: number
          message: string | null
          message_id: number | null
          user_id: number
        }
        Insert: {
          created_at?: string
          id?: number
          message?: string | null
          message_id?: number | null
          user_id?: number
        }
        Update: {
          created_at?: string
          id?: number
          message?: string | null
          message_id?: number | null
          user_id?: number
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          data: Json | null
          id: string
          message: string
          read: boolean | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          data?: Json | null
          id?: string
          message: string
          read?: boolean | null
          title: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string
          data?: Json | null
          id?: string
          message?: string
          read?: boolean | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      packaging_materials: {
        Row: {
          cost_per_unit: number
          created_at: string | null
          id: string
          name: string
        }
        Insert: {
          cost_per_unit: number
          created_at?: string | null
          id?: string
          name: string
        }
        Update: {
          cost_per_unit?: number
          created_at?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      platform_integrations: {
        Row: {
          config: Json
          created_at: string
          error_log: Json | null
          id: string
          last_sync: string | null
          platform: string | null
          status: string | null
          updated_at: string
        }
        Insert: {
          config: Json
          created_at?: string
          error_log?: Json | null
          id?: string
          last_sync?: string | null
          platform?: string | null
          status?: string | null
          updated_at?: string
        }
        Update: {
          config?: Json
          created_at?: string
          error_log?: Json | null
          id?: string
          last_sync?: string | null
          platform?: string | null
          status?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      processing_templates: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          name: string
          template_data: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name: string
          template_data: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name?: string
          template_data?: Json
          updated_at?: string
        }
        Relationships: []
      }
      product_cost: {
        Row: {
          batch_id: string | null
          created_at: string
          dbp_sku: string | null
          id: string
          pcs_sku: string | null
          product_id: string | null
          product_name: string | null
          shipping_id: string | null
          supplier: string | null
          supplier_id: string | null
        }
        Insert: {
          batch_id?: string | null
          created_at?: string
          dbp_sku?: string | null
          id?: string
          pcs_sku?: string | null
          product_id?: string | null
          product_name?: string | null
          shipping_id?: string | null
          supplier?: string | null
          supplier_id?: string | null
        }
        Update: {
          batch_id?: string | null
          created_at?: string
          dbp_sku?: string | null
          id?: string
          pcs_sku?: string | null
          product_id?: string | null
          product_name?: string | null
          shipping_id?: string | null
          supplier?: string | null
          supplier_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_cost_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "batches"
            referencedColumns: ["product_id"]
          },
        ]
      }
      product_forms: {
        Row: {
          created_at: string | null
          id: string
          name: string
          processing_cost_per_pound: number
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          processing_cost_per_pound?: number
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          processing_cost_per_pound?: number
        }
        Relationships: []
      }
      production_events: {
        Row: {
          batch_number: string
          created_at: string
          created_by: string | null
          event_id: string | null
          id: string
          labor_hours: number | null
          quality_check_status: string | null
          recipe_id: string | null
          yield_amount: number | null
          yield_unit: string | null
        }
        Insert: {
          batch_number: string
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          labor_hours?: number | null
          quality_check_status?: string | null
          recipe_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Update: {
          batch_number?: string
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          labor_hours?: number | null
          quality_check_status?: string | null
          recipe_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "production_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      Products: {
        Row: {
          amount: number | null
          category: string | null
          category_id: string | null
          condition: string | null
          cost: number | null
          created_at: string
          date: string | null
          expiry_date: string | null
          handling_instructions: string | null
          id: string
          images: string[] | null
          market_price_history: Json | null
          metadata: Json | null
          min_stock: number
          name: string
          notes: string | null
          origin: string | null
          other_condition: string | null
          price: number | null
          species_details: Json | null
          stock: number | null
          storage_temp: string | null
          sub_category: string | null
          supplier: string | null
          supplier_id: string | null
          unit: string | null
          Unit: string | null
          updated_at: string
        }
        Insert: {
          amount?: number | null
          category?: string | null
          category_id?: string | null
          condition?: string | null
          cost?: number | null
          created_at?: string
          date?: string | null
          expiry_date?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          market_price_history?: Json | null
          metadata?: Json | null
          min_stock?: number
          name: string
          notes?: string | null
          origin?: string | null
          other_condition?: string | null
          price?: number | null
          species_details?: Json | null
          stock?: number | null
          storage_temp?: string | null
          sub_category?: string | null
          supplier?: string | null
          supplier_id?: string | null
          unit?: string | null
          Unit?: string | null
          updated_at?: string
        }
        Update: {
          amount?: number | null
          category?: string | null
          category_id?: string | null
          condition?: string | null
          cost?: number | null
          created_at?: string
          date?: string | null
          expiry_date?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          market_price_history?: Json | null
          metadata?: Json | null
          min_stock?: number
          name?: string
          notes?: string | null
          origin?: string | null
          other_condition?: string | null
          price?: number | null
          species_details?: Json | null
          stock?: number | null
          storage_temp?: string | null
          sub_category?: string | null
          supplier?: string | null
          supplier_id?: string | null
          unit?: string | null
          Unit?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_products_supplier_to_vendors"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          email: string
          full_name: string | null
          id: string
          role: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          role?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          role?: string
          updated_at?: string
        }
        Relationships: []
      }
      quote_items: {
        Row: {
          created_at: string | null
          final_weight: number | null
          id: string
          input_weight: number | null
          price_per_pound: number | null
          product_form_id: string | null
          quote_id: string | null
          species_id: string | null
          total_price: number | null
        }
        Insert: {
          created_at?: string | null
          final_weight?: number | null
          id?: string
          input_weight?: number | null
          price_per_pound?: number | null
          product_form_id?: string | null
          quote_id?: string | null
          species_id?: string | null
          total_price?: number | null
        }
        Update: {
          created_at?: string | null
          final_weight?: number | null
          id?: string
          input_weight?: number | null
          price_per_pound?: number | null
          product_form_id?: string | null
          quote_id?: string | null
          species_id?: string | null
          total_price?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "quote_items_product_form_id_fkey"
            columns: ["product_form_id"]
            isOneToOne: false
            referencedRelation: "product_forms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quote_items_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes"
            referencedColumns: ["id"]
          },
        ]
      }
      quotes: {
        Row: {
          created_at: string | null
          customer_name: string | null
          delivery_timeframe: string | null
          id: string
          packaging_id: string | null
          payment_terms: string | null
          shipping_address: string | null
          shipping_cost: number | null
          shipping_method: string | null
          subtotal: number | null
          total: number | null
        }
        Insert: {
          created_at?: string | null
          customer_name?: string | null
          delivery_timeframe?: string | null
          id?: string
          packaging_id?: string | null
          payment_terms?: string | null
          shipping_address?: string | null
          shipping_cost?: number | null
          shipping_method?: string | null
          subtotal?: number | null
          total?: number | null
        }
        Update: {
          created_at?: string | null
          customer_name?: string | null
          delivery_timeframe?: string | null
          id?: string
          packaging_id?: string | null
          payment_terms?: string | null
          shipping_address?: string | null
          shipping_cost?: number | null
          shipping_method?: string | null
          subtotal?: number | null
          total?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "quotes_packaging_id_fkey"
            columns: ["packaging_id"]
            isOneToOne: false
            referencedRelation: "packaging_materials"
            referencedColumns: ["id"]
          },
        ]
      }
      receiving_events: {
        Row: {
          carrier: string | null
          created_at: string
          created_by: string | null
          event_id: string | null
          id: string
          product_form: string
          quality_check_status: string | null
          quality_notes: string | null
          received_temp: number | null
          shipping_method: string | null
          tracking_number: string | null
          transit_duration: string | null
        }
        Insert: {
          carrier?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          product_form: string
          quality_check_status?: string | null
          quality_notes?: string | null
          received_temp?: number | null
          shipping_method?: string | null
          tracking_number?: string | null
          transit_duration?: string | null
        }
        Update: {
          carrier?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          product_form?: string
          quality_check_status?: string | null
          quality_notes?: string | null
          received_temp?: number | null
          shipping_method?: string | null
          tracking_number?: string | null
          transit_duration?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "receiving_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      reprocessing_events: {
        Row: {
          created_at: string
          created_by: string | null
          event_id: string | null
          id: string
          new_batch_number: string
          quality_check_status: string | null
          reason: string
          source_batch_id: string | null
          yield_amount: number | null
          yield_unit: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          new_batch_number: string
          quality_check_status?: string | null
          reason: string
          source_batch_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          new_batch_number?: string
          quality_check_status?: string | null
          reason?: string
          source_batch_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reprocessing_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reprocessing_events_source_batch_id_fkey"
            columns: ["source_batch_id"]
            isOneToOne: false
            referencedRelation: "production_events"
            referencedColumns: ["id"]
          },
        ]
      }
      sales_events: {
        Row: {
          category: string | null
          channel: string
          created_at: string
          created_by: string | null
          custormer: string | null
          date: string | null
          discount_amount: number | null
          event_id: string | null
          fulfilled_by: string | null
          gross_amount: number
          id: string
          margin_percentage: number | null
          net_amount: string | null
          payment_method: string | null
          price_per_unit: number
          product_name: string | null
          qty: number | null
          sale_type: string | null
          sku: number | null
          unit: string | null
          vendor: string | null
        }
        Insert: {
          category?: string | null
          channel: string
          created_at?: string
          created_by?: string | null
          custormer?: string | null
          date?: string | null
          discount_amount?: number | null
          event_id?: string | null
          fulfilled_by?: string | null
          gross_amount: number
          id?: string
          margin_percentage?: number | null
          net_amount?: string | null
          payment_method?: string | null
          price_per_unit: number
          product_name?: string | null
          qty?: number | null
          sale_type?: string | null
          sku?: number | null
          unit?: string | null
          vendor?: string | null
        }
        Update: {
          category?: string | null
          channel?: string
          created_at?: string
          created_by?: string | null
          custormer?: string | null
          date?: string | null
          discount_amount?: number | null
          event_id?: string | null
          fulfilled_by?: string | null
          gross_amount?: number
          id?: string
          margin_percentage?: number | null
          net_amount?: string | null
          payment_method?: string | null
          price_per_unit?: number
          product_name?: string | null
          qty?: number | null
          sale_type?: string | null
          sku?: number | null
          unit?: string | null
          vendor?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sales_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      shipping_rates: {
        Row: {
          created_at: string | null
          id: string
          max_weight: number | null
          method: string
          min_weight: number | null
          rate_per_pound: number
          region: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          max_weight?: number | null
          method: string
          min_weight?: number | null
          rate_per_pound: number
          region?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          max_weight?: number | null
          method?: string
          min_weight?: number | null
          rate_per_pound?: number
          region?: string | null
        }
        Relationships: []
      }
      shipping_templates: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          name: string
          template_data: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name: string
          template_data: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name?: string
          template_data?: Json
          updated_at?: string
        }
        Relationships: []
      }
      skus: {
        Row: {
          batch_number: string | null
          created_at: string
          id: string
          product_id: string | null
          sku: string
          updated_at: string
          vendor_id: string | null
          vendor_sku: string | null
        }
        Insert: {
          batch_number?: string | null
          created_at?: string
          id?: string
          product_id?: string | null
          sku: string
          updated_at?: string
          vendor_id?: string | null
          vendor_sku?: string | null
        }
        Update: {
          batch_number?: string | null
          created_at?: string
          id?: string
          product_id?: string | null
          sku?: string
          updated_at?: string
          vendor_id?: string | null
          vendor_sku?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "skus_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "skus_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      suppliers: {
        Row: {
          certifications: string[] | null
          contact_name: string | null
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          rating: number | null
          specialties: string[] | null
          updated_at: string
        }
        Insert: {
          certifications?: string[] | null
          contact_name?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          rating?: number | null
          specialties?: string[] | null
          updated_at?: string
        }
        Update: {
          certifications?: string[] | null
          contact_name?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          rating?: number | null
          specialties?: string[] | null
          updated_at?: string
        }
        Relationships: []
      }
      sync_logs: {
        Row: {
          created_at: string
          direction: string | null
          error_details: Json | null
          id: string
          platform_id: string | null
          records_failed: number | null
          records_processed: number | null
          status: string | null
        }
        Insert: {
          created_at?: string
          direction?: string | null
          error_details?: Json | null
          id?: string
          platform_id?: string | null
          records_failed?: number | null
          records_processed?: number | null
          status?: string | null
        }
        Update: {
          created_at?: string
          direction?: string | null
          error_details?: Json | null
          id?: string
          platform_id?: string | null
          records_failed?: number | null
          records_processed?: number | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sync_logs_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platform_integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      vendors: {
        Row: {
          address: string
          batches: string | null
          certifications: string[] | null
          contact_name: string
          created_at: string
          credit_limit: number | null
          email: string
          id: string
          last_order_date: string | null
          metadata: Json | null
          name: string
          notes: string | null
          payment_terms: string | null
          phone: string
          rating: number | null
          specialties: string[] | null
          status: string
          updated_at: string
        }
        Insert: {
          address: string
          batches?: string | null
          certifications?: string[] | null
          contact_name: string
          created_at?: string
          credit_limit?: number | null
          email: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name: string
          notes?: string | null
          payment_terms?: string | null
          phone: string
          rating?: number | null
          specialties?: string[] | null
          status: string
          updated_at?: string
        }
        Update: {
          address?: string
          batches?: string | null
          certifications?: string[] | null
          contact_name?: string
          created_at?: string
          credit_limit?: number | null
          email?: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name?: string
          notes?: string | null
          payment_terms?: string | null
          phone?: string
          rating?: number | null
          specialties?: string[] | null
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_lookup_data: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_sales_tables: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_tables: {
        Args: { table_name: string; schema: string }
        Returns: undefined
      }
      exec_sql: {
        Args: { command: string }
        Returns: Json
      }
      get_columns_for_table: {
        Args: { p_table_name: string }
        Returns: {
          column_name: unknown
          data_type: unknown
        }[]
      }
      get_database_schema: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_public_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: unknown
        }[]
      }
      setup_database: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      sync_inventory_to_platform: {
        Args: { platform_id: string; product_ids: string[] }
        Returns: undefined
      }
      view_database_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
          row_count: number
        }[]
      }
    }
    Enums: {
      condition_type: "fresh" | "frozen" | "other"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      condition_type: ["fresh", "frozen", "other"],
    },
  },
} as const
