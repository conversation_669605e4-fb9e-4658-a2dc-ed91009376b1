
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";

interface ProcessingItem {
    species: string;
    inputWeight: number;
    fromForm: string;
    toForm: string;
    yieldPercentage: number;
    finalWeight: number;
    processingCost: number;
    totalCost: number;
}

interface ProcessingQuote {
    customerName: string;
    items: ProcessingItem[];
    subtotal: number;
    laborCost: number;
    totalCost: number;
}

interface ProcessingQuoteDisplayProps {
    quote: ProcessingQuote | null;
}

export const ProcessingQuoteDisplay = ({ quote }: ProcessingQuoteDisplayProps) => {
    if (!quote) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle>Processing Quote</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div>
                        <strong>Customer:</strong> {quote.customerName}
                    </div>

                    <div className="space-y-2">
                        <strong>Processing Items:</strong>
                        {quote.items.map((item, index) => (
                            <div key={index} className="bg-muted p-3 rounded-md">
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                                    <div><strong>{item.species}</strong></div>
                                    <div>{item.fromForm} → {item.toForm}</div>
                                    <div>{item.inputWeight} lbs → {item.finalWeight.toFixed(2)} lbs</div>
                                    <div className="text-right">${item.totalCost.toFixed(2)}</div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="border-t pt-4">
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span>Processing Subtotal:</span>
                                <span>${quote.subtotal.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                                <span>Labor Cost:</span>
                                <span>${quote.laborCost.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between font-bold text-lg border-t pt-2">
                                <span>Total Cost:</span>
                                <span>${quote.totalCost.toFixed(2)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};
