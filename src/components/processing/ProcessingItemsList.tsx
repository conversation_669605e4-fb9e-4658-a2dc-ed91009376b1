
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ProcessingItem {
    species: string;
    inputWeight: number;
    fromForm: string;
    toForm: string;
    yieldPercentage: number;
    finalWeight: number;
    processingCost: number;
    totalCost: number;
}

interface ProcessingItemsListProps {
    items: ProcessingItem[];
    removeItem: (index: number) => void;
}

export const ProcessingItemsList = ({ items, removeItem }: ProcessingItemsListProps) => {
    if (items.length === 0) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle>Processing Items</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-2">
                    {items.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-md">
                            <div className="flex-1 grid grid-cols-4 gap-4 text-sm">
                                <div>
                                    <strong>{item.species}</strong>
                                    <p className="text-muted-foreground">{item.inputWeight} lbs</p>
                                </div>
                                <div>
                                    <strong>{item.fromForm} → {item.toForm}</strong>
                                    <p className="text-muted-foreground">{item.yieldPercentage}% yield</p>
                                </div>
                                <div>
                                    <strong>Final: {item.finalWeight.toFixed(2)} lbs</strong>
                                </div>
                                <div>
                                    <strong>${item.totalCost.toFixed(2)}</strong>
                                </div>
                            </div>
                            <Button variant="outline" size="sm" onClick={() => removeItem(index)}>
                                Remove
                            </Button>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
};
