
import { useState } from "react";
import { Calculator } from "@/components/Calculator";
import { ShippingCalculator } from "@/components/ShippingCalculator";
import { ProcessingCalculator } from "@/components/ProcessingCalculator";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";

const Index = () => {
  const [activeCalculator, setActiveCalculator] = useState("cost");

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gray-50">
        <AppSidebar activeCalculator={activeCalculator} onCalculatorChange={setActiveCalculator} />
        <main className="flex-1 p-6">
          {activeCalculator === "cost" && <Calculator />}
          {activeCalculator === "shipping" && <ShippingCalculator />}
          {activeCalculator === "processing" && <ProcessingCalculator />}
        </main>
      </div>
    </SidebarProvider>
  );
};

export default Index;
