
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface FormData {
  customerName: string;
  shippingAddress: string;
  totalWeight: string;
  shippingMethod: string;
  packagingMaterial: string;
  notes: string;
}

interface ShippingRate {
  id: string;
  method: string;
  rate_per_pound: number;
  region: string | null;
}

interface PackagingMaterial {
  id: string;
  name: string;
  cost_per_unit: number;
}

interface ShippingDetailsFormProps {
  formData: FormData;
  handleInputChange: (field: string, value: string) => void;
  calculateShipping: () => void;
  resetForm: () => void;
  shippingRates: { data?: ShippingRate[] };
  packagingMaterials: { data?: PackagingMaterial[] };
}

export const ShippingDetailsForm = ({
  formData,
  handleInputChange,
  calculateShipping,
  resetForm,
  shippingRates,
  packagingMaterials,
}: ShippingDetailsFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg sm:text-xl">Shipping Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="sm:col-span-2">
            <Label htmlFor="customerName">Customer Name</Label>
            <Input
              id="customerName"
              value={formData.customerName}
              onChange={(e) => handleInputChange("customerName", e.target.value)}
              placeholder="Enter customer name"
              className="text-base"
            />
          </div>

          <div className="sm:col-span-2">
            <Label htmlFor="shippingAddress">Shipping Address</Label>
            <Textarea
              id="shippingAddress"
              value={formData.shippingAddress}
              onChange={(e) => handleInputChange("shippingAddress", e.target.value)}
              placeholder="Enter complete shipping address"
              rows={3}
              className="text-base resize-none"
            />
          </div>

          <div>
            <Label htmlFor="totalWeight">Total Weight (lbs)</Label>
            <Input
              id="totalWeight"
              type="number"
              step="0.01"
              value={formData.totalWeight}
              onChange={(e) => handleInputChange("totalWeight", e.target.value)}
              placeholder="0.00"
              className="text-base"
            />
          </div>

          <div>
            <Label htmlFor="packagingMaterial">Packaging Material</Label>
            <Select value={formData.packagingMaterial} onValueChange={(value) => handleInputChange("packagingMaterial", value)}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select packaging material" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                {packagingMaterials.data?.map((material) => (
                  <SelectItem key={material.id} value={material.name}>
                    {material.name} - ${material.cost_per_unit}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="sm:col-span-2">
            <Label htmlFor="shippingMethod">Shipping Method (Optional)</Label>
            <Select value={formData.shippingMethod} onValueChange={(value) => handleInputChange("shippingMethod", value)}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select shipping method (optional)" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                <SelectItem value="">No shipping method</SelectItem>
                {shippingRates.data?.map((rate) => (
                  <SelectItem key={rate.id} value={rate.method}>
                    {rate.method} - ${rate.rate_per_pound}/lb
                    {rate.region && ` (${rate.region})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="sm:col-span-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Additional notes or special instructions"
              rows={2}
              className="text-base resize-none"
            />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 pt-4">
          <Button onClick={calculateShipping} className="flex-1 text-base py-3 sm:py-2">
            Calculate Shipping
          </Button>
          <Button variant="outline" onClick={resetForm} className="text-base py-3 sm:py-2">
            Reset
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
