
import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface FormData {
  speciesName: string;
  fromProductForm: string;
  toProductForm: string;
  averagePercentage: string;
  rangePercentage: string;
  productFormId: string;
  rawCost: string;
  weight: string;
  yield: string;
  shippingRateId: string;
  packagingId: string;
  markup: string;
}

interface Option {
    id: string;
    name: string;
}

interface CalculatorFormProps {
  formData: FormData;
  handleInputChange: (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange: (field: string) => (value: string) => void;
  calculateResults: () => void;
  species: { data?: { id: string; name: string }[] };
  productForms: { data?: { id: string; name: string }[] };
  shippingRates: { data?: { id: string; method: string; region: string | null; rate_per_pound: number }[] };
  packagingMaterials: { data?: { id: string; name: string; cost_per_unit: number }[] };
  fromOptions: Option[];
  toOptions: Option[];
}

export const CalculatorForm = ({
  formData,
  handleInputChange,
  handleSelectChange,
  calculateResults,
  species,
  productForms,
  shippingRates,
  packagingMaterials,
  fromOptions,
  toOptions,
}: CalculatorFormProps) => {
    return (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6">Calculate Costs</h2>
          
          <div className="space-y-6">
            <div>
              <Label className="calculator-label">Species ({species.data?.length || 0} available)</Label>
              <Select onValueChange={handleSelectChange("speciesName")} value={formData.speciesName}>
                <SelectTrigger className="calculator-select">
                  <SelectValue placeholder={species.data?.length ? "Select species" : "No species available"} />
                </SelectTrigger>
                <SelectContent>
                  {species.data?.length ? (
                    species.data.map((speciesItem) => (
                      <SelectItem key={speciesItem.id} value={speciesItem.name}>
                        {speciesItem.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-data" disabled>
                      No species data available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="calculator-label">From Product Form</Label>
              <Select 
                value={formData.fromProductForm} 
                onValueChange={handleSelectChange("fromProductForm")}
                disabled={!formData.speciesName}
              >
                <SelectTrigger className="calculator-select">
                  <SelectValue placeholder="Select from product form" />
                </SelectTrigger>
                <SelectContent>
                  {fromOptions.map((option) => (
                    <SelectItem key={option.id} value={option.name}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="calculator-label">To Product Form</Label>
              <Select 
                value={formData.toProductForm} 
                onValueChange={handleSelectChange("toProductForm")}
                disabled={!formData.fromProductForm}
              >
                <SelectTrigger className="calculator-select">
                  <SelectValue placeholder="Select to product form" />
                </SelectTrigger>
                <SelectContent>
                  {toOptions.map((option) => (
                    <SelectItem key={option.id} value={option.name}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="calculator-label">Average Percentage</Label>
              <Input
                type="text"
                className="calculator-input"
                placeholder="Auto-filled from selection"
                value={formData.averagePercentage}
                readOnly
              />
            </div>

            <div>
              <Label className="calculator-label">Range Percentage</Label>
              <Input
                type="text"
                className="calculator-input"
                placeholder="Auto-filled from selection"
                value={formData.rangePercentage}
                readOnly
              />
            </div>

            <div>
              <Label className="calculator-label">Processing Product Form</Label>
              <Select onValueChange={handleSelectChange("productFormId")} value={formData.productFormId}>
                <SelectTrigger className="calculator-select">
                  <SelectValue placeholder="Select processing product form" />
                </SelectTrigger>
                <SelectContent>
                  {productForms.data?.map((form) => (
                    <SelectItem key={form.id} value={form.id}>
                      {form.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="calculator-label">Raw Product Cost (USD per lb)</Label>
              <Input
                type="number"
                className="calculator-input"
                placeholder="0.00"
                value={formData.rawCost}
                onChange={handleInputChange("rawCost")}
              />
            </div>

            <div>
              <Label className="calculator-label">Weight (lbs)</Label>
              <Input
                type="number"
                className="calculator-input"
                placeholder="0"
                value={formData.weight}
                onChange={handleInputChange("weight")}
              />
            </div>

            <div>
              <Label className="calculator-label">Yield %</Label>
              <Input
                type="number"
                className="calculator-input"
                placeholder="0"
                value={formData.yield}
                onChange={handleInputChange("yield")}
              />
            </div>

            <div>
              <Label className="calculator-label">Shipping Rate</Label>
              <Select onValueChange={handleSelectChange("shippingRateId")} value={formData.shippingRateId}>
                <SelectTrigger className="calculator-select">
                  <SelectValue placeholder="Select shipping rate" />
                </SelectTrigger>
                <SelectContent>
                  {shippingRates.data?.map((rate) => (
                    <SelectItem key={rate.id} value={rate.id}>
                      {`${rate.method} (${rate.region || 'All'}) - $${rate.rate_per_pound}/lb`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="calculator-label">Packaging (Optional)</Label>
              <Select onValueChange={handleSelectChange("packagingId")} value={formData.packagingId}>
                <SelectTrigger className="calculator-select">
                  <SelectValue placeholder="Select packaging" />
                </SelectTrigger>
                <SelectContent>
                  {packagingMaterials.data?.map((packaging) => (
                    <SelectItem key={packaging.id} value={packaging.id}>
                      {`${packaging.name} - $${packaging.cost_per_unit}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="calculator-label">Markup % (Optional)</Label>
              <Input
                type="number"
                className="calculator-input"
                placeholder="0"
                value={formData.markup}
                onChange={handleInputChange("markup")}
              />
            </div>

            <Button 
              onClick={calculateResults}
              className="w-full"
            >
              Calculate Costs
            </Button>
          </div>
        </Card>
    )
}
