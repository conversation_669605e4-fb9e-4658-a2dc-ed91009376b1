
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface FormData {
    customerName: string;
    species: string;
    inputWeight: string;
    fromForm: string;
    toForm: string;
    laborHours: string;
    laborRate: string;
    notes: string;
}

interface ProcessingLaborAndQuoteFormProps {
    formData: FormData;
    handleInputChange: (field: string, value: string) => void;
    generateQuote: () => void;
    resetForm: () => void;
    itemsCount: number;
}

export const ProcessingLaborAndQuoteForm = ({
    formData,
    handleInputChange,
    generateQuote,
    resetForm,
    itemsCount,
}: ProcessingLaborAndQuoteFormProps) => {
    if (itemsCount === 0) return null;

    return (
        <Card>
            <CardHeader>
                <CardTitle>Labor & Final Quote</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label htmlFor="laborHours">Labor Hours</Label>
                        <Input
                            id="laborHours"
                            type="number"
                            step="0.1"
                            value={formData.laborHours}
                            onChange={(e) => handleInputChange("laborHours", e.target.value)}
                            placeholder="0.0"
                        />
                    </div>
                    <div>
                        <Label htmlFor="laborRate">Labor Rate ($/hour)</Label>
                        <Input
                            id="laborRate"
                            type="number"
                            step="0.01"
                            value={formData.laborRate}
                            onChange={(e) => handleInputChange("laborRate", e.target.value)}
                        />
                    </div>
                </div>

                <div>
                    <Label htmlFor="notes">Notes (Optional)</Label>
                    <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange("notes", e.target.value)}
                        placeholder="Additional processing notes or special instructions"
                        rows={2}
                    />
                </div>

                <div className="flex gap-2">
                    <Button onClick={generateQuote} className="flex-1">
                        Generate Quote
                    </Button>
                    <Button variant="outline" onClick={resetForm}>
                        Reset All
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
};
