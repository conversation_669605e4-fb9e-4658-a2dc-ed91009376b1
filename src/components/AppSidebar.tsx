
import { Calculator, Ship, ScrollText } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

interface AppSidebarProps {
  activeCalculator: string;
  onCalculatorChange: (calculator: string) => void;
}

export function AppSidebar({ activeCalculator, onCalculatorChange }: AppSidebarProps) {
  const items = [
    {
      title: "Cost Calculator",
      icon: Calculator,
      id: "cost",
    },
    {
      title: "Shipping Calculator",
      icon: Ship,
      id: "shipping",
    },
    {
      title: "Processing Calculator",
      icon: ScrollText,
      id: "processing",
    },
  ];

  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Calculators</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    onClick={() => onCalculatorChange(item.id)}
                    className={activeCalculator === item.id ? "bg-primary/10" : ""}
                  >
                    <item.icon className="w-5 h-5" />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
