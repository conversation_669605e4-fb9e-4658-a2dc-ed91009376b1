// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://puzjricwpsjusjlgrwen.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE3NTU3MTgsImV4cCI6MjA1NzMzMTcxOH0.zC8AN8oLj_7QJrMFiqiyze-IZz2nNcyFpMFGUvMQF48";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);